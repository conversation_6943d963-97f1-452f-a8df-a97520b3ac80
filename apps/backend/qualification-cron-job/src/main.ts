import { Logger } from '@aws-lambda-powertools/logger';
import { DynamoDBClient, PutItemCommand } from '@aws-sdk/client-dynamodb';
import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from '@aws-sdk/client-secrets-manager';
import { marshall } from '@aws-sdk/util-dynamodb';
import { Context, ScheduledEvent } from 'aws-lambda';
import axios, { AxiosResponse } from 'axios';

import { initSentryLambda, wrapSentryHandler } from '@s2a/sentry-helper';
import {
  OAuthTokenResponse,
  QualificationsResponseData,
  AccountQualifications,
  SecretValue,
  QualificationDB,
} from '@shift-management/api/qualifications';

const logger = new Logger({
  serviceName: 'qualification-cron-job',
});

initSentryLambda();

/**
 * Retrieve client credentials from AWS Secrets Manager
 * @returns Promise that resolves to client credentials
 */
async function getSecret(): Promise<SecretValue> {
  const secretName = process.env['SECRET_NAME'];
  const region = process.env['AWS_REGION'] || 'eu-central-1';

  if (!secretName) {
    throw new Error('SECRET_NAME environment variable is not set');
  }

  const client = new SecretsManagerClient({ region });

  try {
    const command = new GetSecretValueCommand({
      SecretId: secretName,
    });

    const response = await client.send(command);

    if (!response.SecretString) {
      throw new Error('Secret value is empty');
    }

    const secret: SecretValue = JSON.parse(response.SecretString);

    if (!secret.client_id || !secret.client_secret) {
      throw new Error('Secret must contain client_id and client_secret');
    }

    return secret;
  } catch (error) {
    logger.error('Error retrieving secret:', { error });
    throw error;
  }
}

/**
 * Fetch qualifications data using the access token
 * @param token - The access token for API authentication
 * @returns Promise that resolves to qualification data
 */
async function getQualificationsData(
  token: string,
): Promise<AccountQualifications[]> {
  const url = process.env['QUALIFICATION_API_URL'];

  if (!url) {
    throw new Error('QUALIFICATION_API_URL environment variable is not set');
  }

  try {
    const response: AxiosResponse<QualificationsResponseData> = await axios.get(
      url,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        timeout: 30000, // 30 seconds timeout
      },
    );
    logger.info('Successfully retrieved qualifications data', {
      statusCode: response.status,
      body: response.data,
    });

    // Transform the response to match our internal format
    if (response.data?.Customers && Array.isArray(response.data.Customers)) {
      const customers = response.data.Customers;
      return customers.map((customer) => ({
        account: customer.Customer,
        value: customer.value.map((apiQualification) => ({
          qualificationId: apiQualification.qualificationID,
          description: apiQualification.description,
          account: customer.Customer,
          entityType: 'QUALIFICATION',
          texts: apiQualification.texts,
        })),
      }));
    }

    throw new Error("Missing 'Customers' array in response data");
  } catch (error) {
    logger.error('Error fetching qualifications:', { error });
    throw error;
  }
}

/**
 * Write qualification data to DynamoDB table
 * @param accountQualifications - Array of account qualifications to write
 */
async function writeQualificationsToDynamoDB(
  accountQualifications: AccountQualifications[],
): Promise<void> {
  const tableName = process.env['SHIFT_DATA_TABLE_NAME'];
  const region = process.env['AWS_REGION'] || 'eu-central-1';

  if (!tableName) {
    throw new Error('SHIFT_DATA_TABLE_NAME environment variable is not set');
  }

  const dynamoClient = new DynamoDBClient({ region });

  try {
    let totalQualificationsWritten = 0;

    for (const accountData of accountQualifications) {
      for (const qualification of accountData.value) {
        const qualificationDB = new QualificationDB();

        qualificationDB.qualificationId = qualification.qualificationId;
        qualificationDB.description = qualification.description;
        qualificationDB.account = accountData.account;
        qualificationDB.entityType = 'QUALIFICATION';
        qualificationDB.texts = qualification.texts; // Include multilingual texts

        qualificationDB.PK = `A#${accountData.account}`; // Account partition key with prefix
        qualificationDB.SK = `Q#${qualification.qualificationId}`; // Qualification sort key with prefix
        qualificationDB.updatedAt = Date.now();

        const command = new PutItemCommand({
          TableName: tableName,
          Item: marshall(qualificationDB),
        });

        await dynamoClient.send(command);
        totalQualificationsWritten++;

        logger.debug(
          `Successfully wrote qualification ${qualification.qualificationId} for account: ${accountData.account}`,
          {
            account: accountData.account,
            qualificationId: qualification.qualificationId,
            description: qualification.description,
          },
        );
      }

      logger.info(
        `Successfully wrote ${accountData.value.length} qualifications for account: ${accountData.account}`,
        {
          account: accountData.account,
          qualificationCount: accountData.value.length,
        },
      );
    }

    logger.info(
      `Total qualifications written to DynamoDB: ${totalQualificationsWritten}`,
    );
  } catch (error) {
    logger.error('Error writing qualifications to DynamoDB:', { error });
    throw error;
  }
}

/**
 * Main Lambda handler function
 * @param event - The scheduled event from EventBridge
 * @param context - The Lambda context
 * @returns Promise that resolves to the response object
 */
export const handler = wrapSentryHandler(
  async (event: ScheduledEvent, context: Context) => {
    logger.info('Daily qualification cron job triggered', { event, context });

    try {
      // Get credentials from Secrets Manager
      const { client_id, client_secret } = await getSecret();

      // OAuth token endpoint
      const tokenUrl = process.env['TOKEN_URL'];

      if (!tokenUrl) {
        throw new Error('TOKEN_URL environment variable is not set');
      }

      const tokenPayload = new URLSearchParams({
        grant_type: 'client_credentials',
        client_id,
        client_secret,
      });

      // Get access token
      const tokenResponse: AxiosResponse<OAuthTokenResponse> = await axios.post(
        tokenUrl,
        tokenPayload,
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          timeout: 30000,
        },
      );

      const { access_token } = tokenResponse.data;
      logger.info('Successfully retrieved access token', { access_token });

      // Get qualifications data
      const qualificationsData = await getQualificationsData(access_token);

      // Calculate total qualification count across all accounts
      const totalQualificationCount = qualificationsData.reduce(
        (total, account) => total + account.value.length,
        0,
      );

      logger.info(
        `Successfully retrieved qualifications for ${qualificationsData.length} accounts`,
        {
          accountCount: qualificationsData.length,
          totalQualifications: totalQualificationCount,
          accounts: qualificationsData.map((acc) => ({
            account: acc.account,
            qualificationCount: acc.value.length,
          })),
        },
      );

      // Write qualifications to DynamoDB
      await writeQualificationsToDynamoDB(qualificationsData);

      logger.info('Successfully wrote all qualifications to DynamoDB');

      return {
        statusCode: 200,
        body: JSON.stringify({
          message: 'Qualifications retrieved and stored successfully',
          accountCount: qualificationsData.length,
          totalQualificationCount,
          timestamp: new Date().toISOString(),
          accounts: qualificationsData.map((acc) => ({
            account: acc.account,
            qualificationCount: acc.value.length,
          })),
        }),
      };
    } catch (error) {
      logger.error('Error in qualification cron job:', { error });

      return {
        statusCode: 500,
        body: JSON.stringify({
          error: 'Internal server error',
          message: error instanceof Error ? error.message : 'Unknown error',
          timestamp: new Date().toISOString(),
        }),
      };
    }
  },
);
