import {
  SecretsManagerClient,
  GetSecretValueCommand,
} from '@aws-sdk/client-secrets-manager';
import { Context, ScheduledEvent } from 'aws-lambda';
import axios from 'axios';

interface LambdaResponse {
  statusCode: number;
  body: string;
}

// Mock AWS SDK and DynamoDB client provider
jest.mock('@aws-sdk/client-secrets-manager');
jest.mock('@shift-management/api/util/dynamodb-client');
jest.mock('axios');
jest.mock('@aws-lambda-powertools/logger');
jest.mock('@s2a/sentry-helper', () => ({
  initSentryLambda: jest.fn(),
  wrapSentryHandler: jest.fn((handler: unknown) => handler),
}));

const mockedSecretsManagerClient = jest.mocked(SecretsManagerClient);
const mockedAxios = jest.mocked(axios);

describe('Qualification Cron Job Handler', () => {
  let mockSecretsManagerSend: jest.Mock;
  let mockDynamoDBInsert: jest.Mock;
  let handler: (
    event: ScheduledEvent,
    context: Context,
  ) => Promise<LambdaResponse>;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock SecretsManager client
    mockSecretsManagerSend = jest.fn();
    mockedSecretsManagerClient.prototype.send = mockSecretsManagerSend;

    // Mock DynamoDB client provider
    mockDynamoDBInsert = jest.fn();
    const { DynamoDBClientProvider } = jest.requireMock(
      '@shift-management/api/util/dynamodb-client',
    );
    DynamoDBClientProvider.mockImplementation(() => ({
      insert: mockDynamoDBInsert,
    }));

    // Mock environment variables
    process.env['SECRET_NAME'] = 'test-secret';
    process.env['SHIFT_DATA_TABLE_NAME'] = 'test-table';
    process.env['AWS_REGION'] = 'eu-central-1';
    process.env['QUALIFICATION_API_URL'] =
      'https://oem-services-qpsffewd.it-cpi024-rt.cfapps.eu10-002.hana.ondemand.com/http/Datalayer/Qualifications/texts';
    process.env['TOKEN_URL'] =
      'https://oem-services-qpsffewd.authentication.eu10.hana.ondemand.com/oauth/token';

    // Import handler after mocks are set up
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    handler = require('./main').handler;
  });

  afterEach(() => {
    delete process.env['SECRET_NAME'];
    delete process.env['SHIFT_DATA_TABLE_NAME'];
    delete process.env['AWS_REGION'];
    delete process.env['QUALIFICATION_API_URL'];
    delete process.env['TOKEN_URL'];
  });

  const mockEvent: ScheduledEvent = {
    version: '0',
    id: 'test-id',
    // eslint-disable-next-line @typescript-eslint/naming-convention
    'detail-type': 'Scheduled Event',
    source: 'aws.events',
    account: '************',
    time: '2023-01-01T00:00:00Z',
    region: 'eu-central-1',
    detail: {},
    resources: ['arn:aws:events:eu-central-1:************:rule/test-rule'],
  };

  const mockContext: Context = {
    callbackWaitsForEmptyEventLoop: false,
    functionName: 'test-function',
    functionVersion: '1',
    invokedFunctionArn:
      'arn:aws:lambda:eu-central-1:************:function:test-function',
    memoryLimitInMB: '128',
    awsRequestId: 'test-request-id',
    logGroupName: '/aws/lambda/test-function',
    logStreamName: '2023/01/01/[$LATEST]test-stream',
    getRemainingTimeInMillis: () => 30000,
    done: jest.fn(),
    fail: jest.fn(),
    succeed: jest.fn(),
  };

  it('should successfully process qualification data', async () => {
    // Mock secrets manager response
    mockSecretsManagerSend.mockResolvedValueOnce({
      SecretString: JSON.stringify({
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
      }),
    });

    // Mock OAuth token response
    mockedAxios.post.mockResolvedValueOnce({
      data: {
        access_token: 'test-access-token',
        token_type: 'Bearer',
        expires_in: 3600,
      },
    });

    // Mock qualifications API response with proper structure matching real API
    const mockQualificationsResponse = {
      Customers: [
        {
          Customer: 'account-1',
          value: [
            {
              qualificationID: 'ShrinkTBC',
              description: 'Shrink Gauge TBC',
              texts: [
                { locale: 'de', description: 'Shrink Gauge TBC' },
                { locale: 'en', description: 'Shrink Gauge TBC' },
              ],
            },
            {
              qualificationID: 'No night shift',
              description: 'Qualification Dominik and Konni',
              texts: [
                {
                  locale: 'de',
                  description: 'Qualification Dominik and Konni',
                },
                {
                  locale: 'en',
                  description: 'Qualification Dominik and Konni',
                },
              ],
            },
            {
              qualificationID: 'Test RSL 001',
              description: 'Test-Quali RSL 001',
              texts: [
                { locale: 'de', description: 'Test-Quali RSL 001' },
                { locale: 'en', description: 'Test-Quali RSL 001' },
              ],
            },
            {
              qualificationID: 'Laboratory',
              description: 'laboratory',
              texts: [
                { locale: 'de', description: 'Laboratory' },
                { locale: 'en', description: 'laboratory' },
              ],
            },
            {
              qualificationID: 'CO-Quali',
              description: 'CO-Quali',
              texts: [
                { locale: 'de', description: 'CO-Quali' },
                { locale: 'en', description: 'CO-Quali' },
              ],
            },
            {
              qualificationID: 'Quality ',
              description: 'Quality assurance department',
              texts: [
                { locale: 'de', description: 'Qualitätskontrolle' },
                { locale: 'en', description: 'Quality assurance department' },
                { locale: 'fr', description: 'quality departement' },
              ],
            },
            {
              qualificationID: 'Training Test Qualification',
              description: 'Training Test Qualification',
              texts: [
                { locale: 'de', description: 'Training Test Qualification' },
                { locale: 'en', description: 'Training Test Qualification' },
              ],
            },
          ],
        },
      ],
    };

    mockedAxios.get.mockResolvedValueOnce({
      data: mockQualificationsResponse,
    });

    // Mock DynamoDB responses
    mockDynamoDBInsert.mockResolvedValue({});

    const result = await handler(mockEvent, mockContext);

    expect(result.statusCode).toBe(200);
    expect(JSON.parse(result.body)).toEqual({
      message: 'Qualifications retrieved and stored successfully',
      accountCount: 1,
      timestamp: expect.any(String),
      accounts: [{ account: 'account-1', qualificationCount: 7 }],
    });

    // Verify secrets manager was called
    expect(mockSecretsManagerSend).toHaveBeenCalledWith(
      expect.any(GetSecretValueCommand),
    );

    // Verify OAuth request
    expect(mockedAxios.post).toHaveBeenCalledWith(
      process.env['TOKEN_URL'],
      expect.any(URLSearchParams),
      expect.objectContaining({
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        timeout: 30000,
      }),
    );

    // Verify qualifications API request
    expect(mockedAxios.get).toHaveBeenCalledWith(
      process.env['QUALIFICATION_API_URL'],
      expect.objectContaining({
        headers: {
          Authorization: 'Bearer test-access-token',
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      }),
    );

    // Verify DynamoDB writes (1 account with 7 qualifications = 7 insert calls)
    expect(mockDynamoDBInsert).toHaveBeenCalledTimes(7);
    expect(mockDynamoDBInsert).toHaveBeenCalledWith(
      expect.objectContaining({
        TableName: 'test-table',
        Item: expect.any(Object),
      }),
    );
  });

  it('should use default table name when SHIFT_DATA_TABLE_NAME is not set', async () => {
    delete process.env['SHIFT_DATA_TABLE_NAME'];

    // Mock secrets manager response
    mockSecretsManagerSend.mockResolvedValueOnce({
      SecretString: JSON.stringify({
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
      }),
    });

    // Mock OAuth token response
    mockedAxios.post.mockResolvedValueOnce({
      data: {
        access_token: 'test-access-token',
        token_type: 'Bearer',
        expires_in: 3600,
      },
    });

    // Mock qualifications API response
    mockedAxios.get.mockResolvedValueOnce({
      data: {
        Customers: [
          {
            Customer: 'account-1',
            value: [
              {
                qualificationID: 'test-qualification',
                description: 'test description',
                texts: [
                  { locale: 'de', description: 'test description' },
                  { locale: 'en', description: 'test description' },
                ],
              },
            ],
          },
        ],
      },
    });

    // Mock DynamoDB responses
    mockDynamoDBInsert.mockResolvedValue({});

    const result = await handler(mockEvent, mockContext);

    expect(result.statusCode).toBe(200);

    // Verify DynamoDB insert was called with default table name
    expect(mockDynamoDBInsert).toHaveBeenCalledWith(
      expect.objectContaining({
        TableName: 's2a-shift-service-shift-data',
        Item: expect.any(Object),
      }),
    );
  });

  it('should handle missing SECRET_NAME environment variable', async () => {
    delete process.env['SECRET_NAME'];

    const result = await handler(mockEvent, mockContext);

    expect(result.statusCode).toBe(500);
    expect(JSON.parse(result.body)).toEqual({
      error: 'Internal server error',
      message: 'SECRET_NAME environment variable is not set',
      timestamp: expect.any(String),
    });
  });

  it('should handle missing QUALIFICATION_API_URL environment variable', async () => {
    delete process.env['QUALIFICATION_API_URL'];

    // Mock secrets manager response
    mockSecretsManagerSend.mockResolvedValueOnce({
      SecretString: JSON.stringify({
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
      }),
    });

    // Mock OAuth token response
    mockedAxios.post.mockResolvedValueOnce({
      data: {
        access_token: 'test-access-token',
        token_type: 'Bearer',
        expires_in: 3600,
      },
    });

    const result = await handler(mockEvent, mockContext);

    expect(result.statusCode).toBe(500);
    expect(JSON.parse(result.body)).toEqual({
      error: 'Internal server error',
      message: 'QUALIFICATION_API_URL environment variable is not set',
      timestamp: expect.any(String),
    });
  });

  it('should handle missing TOKEN_URL environment variable', async () => {
    delete process.env['TOKEN_URL'];

    // Mock secrets manager response
    mockSecretsManagerSend.mockResolvedValueOnce({
      SecretString: JSON.stringify({
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
      }),
    });

    const result = await handler(mockEvent, mockContext);

    expect(result.statusCode).toBe(500);
    expect(JSON.parse(result.body)).toEqual({
      error: 'Internal server error',
      message: 'TOKEN_URL environment variable is not set',
      timestamp: expect.any(String),
    });
  });

  it('should handle secrets manager errors', async () => {
    mockSecretsManagerSend.mockRejectedValueOnce(
      new Error('Secrets Manager error'),
    );

    const result = await handler(mockEvent, mockContext);

    expect(result.statusCode).toBe(500);
    expect(JSON.parse(result.body)).toEqual({
      error: 'Internal server error',
      message: 'Secrets Manager error',
      timestamp: expect.any(String),
    });
  });

  it('should handle OAuth token request errors', async () => {
    // Mock secrets manager response
    mockSecretsManagerSend.mockResolvedValueOnce({
      SecretString: JSON.stringify({
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
      }),
    });

    // Mock OAuth token error
    mockedAxios.post.mockRejectedValueOnce(new Error('OAuth error'));

    const result = await handler(mockEvent, mockContext);

    expect(result.statusCode).toBe(500);
    expect(JSON.parse(result.body)).toEqual({
      error: 'Internal server error',
      message: 'OAuth error',
      timestamp: expect.any(String),
    });
  });

  it('should handle qualifications API errors', async () => {
    // Mock secrets manager response
    mockSecretsManagerSend.mockResolvedValueOnce({
      SecretString: JSON.stringify({
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
      }),
    });

    // Mock OAuth token response
    mockedAxios.post.mockResolvedValueOnce({
      data: {
        access_token: 'test-access-token',
        token_type: 'Bearer',
        expires_in: 3600,
      },
    });

    // Mock qualifications API error
    mockedAxios.get.mockRejectedValueOnce(new Error('API error'));

    const result = await handler(mockEvent, mockContext);

    expect(result.statusCode).toBe(500);
    expect(JSON.parse(result.body)).toEqual({
      error: 'Internal server error',
      message: 'API error',
      timestamp: expect.any(String),
    });
  });

  it('should handle DynamoDB write errors', async () => {
    // Mock successful API calls
    mockSecretsManagerSend.mockResolvedValueOnce({
      SecretString: JSON.stringify({
        client_id: 'test-client-id',
        client_secret: 'test-client-secret',
      }),
    });

    mockedAxios.post.mockResolvedValueOnce({
      data: { access_token: 'test-access-token' },
    });

    mockedAxios.get.mockResolvedValueOnce({
      data: {
        Customers: [
          {
            Customer: 'account-1',
            value: [
              {
                qualificationID: 'test-qualification',
                description: 'test description',
                texts: [
                  { locale: 'de', description: 'test description' },
                  { locale: 'en', description: 'test description' },
                ],
              },
            ],
          },
        ],
      },
    });

    // Mock DynamoDB error
    mockDynamoDBInsert.mockRejectedValueOnce(new Error('DynamoDB error'));

    const result = await handler(mockEvent, mockContext);

    expect(result.statusCode).toBe(500);
    expect(JSON.parse(result.body)).toEqual({
      error: 'Internal server error',
      message: 'DynamoDB error',
      timestamp: expect.any(String),
    });
  });
});
