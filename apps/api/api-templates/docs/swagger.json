{"openapi": "3.0.0", "paths": {"/templates/active": {"get": {"operationId": "getActiveTemplatesForAccount", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActiveTemplatesResponse"}}}}}, "tags": ["templates"]}}, "/templates/active/line/{lineId}": {"get": {"operationId": "getActiveTemplateForLine", "parameters": [{"name": "lineId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["templates"]}, "put": {"operationId": "updateActiveTemplateForLine", "parameters": [{"name": "lineId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActiveTemplateDto"}}}}, "responses": {"200": {"description": ""}}, "tags": ["templates"]}}, "/templates": {"get": {"operationId": "getAllTemplatesForAccount", "summary": "", "description": "Get All Templates for a specific account", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TemplatesResponse"}}}}}, "tags": ["templates"]}, "post": {"operationId": "createTemplateForAccount", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTemplate"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}}, "tags": ["templates"]}}, "/templates/template/{templateId}": {"get": {"operationId": "getTemplateById", "parameters": [{"name": "templateId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}}, "tags": ["templates"]}, "patch": {"operationId": "patchTemplateById", "parameters": [{"name": "templateId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTemplate"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Template"}}}}}, "tags": ["templates"]}, "delete": {"operationId": "deleteTemplateById", "parameters": [{"name": "templateId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["templates"]}}}, "info": {"title": "shift-management-api-templates", "description": "Shift API Templates", "version": "1.0", "contact": {}}, "tags": [], "servers": [], "components": {"schemas": {"ActiveTemplate": {"type": "object", "properties": {"lineId": {"type": "string"}, "account": {"type": "string"}, "lineName": {"type": "string"}, "activeTemplateId": {"type": "string"}, "fallbackTemplateId": {"type": "string"}}, "required": ["lineId", "account", "lineName", "activeTemplateId", "fallbackTemplateId"]}, "ActiveTemplatesResponse": {"type": "object", "properties": {"activeTemplates": {"type": "array", "items": {"$ref": "#/components/schemas/ActiveTemplate"}}}, "required": ["activeTemplates"]}, "ActiveTemplateDto": {"type": "object", "properties": {"activeTemplateId": {"type": "string"}, "fallbackTemplateId": {"type": "string"}}, "required": ["activeTemplateId", "fallbackTemplateId"]}, "ScheduleItem": {"type": "object", "properties": {"class": {"type": "string", "enum": ["productive", "changeover", "cleaning", "maintenance", "testProduction", "other"]}, "start": {"type": "number"}, "end": {"type": "number"}, "name": {"type": "string"}, "qualification": {"type": "string"}}, "required": ["class", "start", "end", "name"]}, "Template": {"type": "object", "properties": {"id": {"type": "string"}, "account": {"type": "string"}, "type": {"enum": ["userDefined", "predefined"], "type": "string"}, "shiftsPerDay": {"type": "number"}, "shiftStartTime": {"type": "string"}, "name": {"type": "string"}, "schedule": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleItem"}}}, "required": ["id", "account", "type", "shiftsPerDay", "shiftStartTime", "name", "schedule"]}, "TemplatesResponse": {"type": "object", "properties": {"templates": {"type": "array", "items": {"$ref": "#/components/schemas/Template"}}}, "required": ["templates"]}, "CreateTemplate": {"type": "object", "properties": {"shiftsPerDay": {"type": "number"}, "shiftStartTime": {"type": "string"}, "name": {"type": "string"}, "schedule": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleItem"}}}, "required": ["shiftsPerDay", "shiftStartTime", "name", "schedule"]}, "UpdateTemplate": {"type": "object", "properties": {"name": {"type": "string"}, "schedule": {"type": "array", "items": {"$ref": "#/components/schemas/ScheduleItem"}}}}}}}