import { DynamoDBDocumentClient } from '@aws-sdk/lib-dynamodb';
import { Test, TestingModule } from '@nestjs/testing';
import { mockClient } from 'aws-sdk-client-mock';

import { DynamoDBClientProvider } from '@shift-management/api/util/dynamodb-client';

import { ApiQualificationsService } from './api-qualifications.service';
import { QualificationDB } from './qualification.entity';

const dynDbMock = mockClient(DynamoDBDocumentClient);

describe('ApiQualificationsService', () => {
  let service: ApiQualificationsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ApiQualificationsService,
        {
          provide: DynamoDBClientProvider,
          useValue: {
            queryAll: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<ApiQualificationsService>(ApiQualificationsService);
    dynDbMock.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getQualificationsForAccount', () => {
    it('should throw error if account is not provided', async () => {
      await expect(service.getQualificationsForAccount('')).rejects.toThrow(
        'getQualificationsForAccount: account is required',
      );
    });

    it('should return qualifications for account', async () => {
      const mockQualificationDB: QualificationDB = {
        PK: 'A#test-account',
        SK: 'Q#test-qualification',
        qualificationId: 'test-qualification',
        description: 'Test qualification description',
        account: 'test-account',
        entityType: 'QUALIFICATION',
        updatedAt: *************,
        TTL: **********,
      } as QualificationDB;

      const dynamodbClient = service.dynamodbClient;
      jest
        .spyOn(dynamodbClient, 'queryAll')
        .mockResolvedValue([mockQualificationDB]);

      const result = await service.getQualificationsForAccount('test-account');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        qualificationId: 'test-qualification',
        description: 'Test qualification description',
        account: 'test-account',
        entityType: 'QUALIFICATION',
      });
    });

    it('should work with plain JavaScript objects using static from method', async () => {
      // This simulates what actually happens when DynamoDB client returns plain objects
      const mockPlainObject: QualificationDB = {
        PK: 'A#test-account',
        SK: 'Q#test-qualification',
        qualificationId: 'test-qualification',
        description: 'Test qualification description',
        account: 'test-account',
        entityType: 'QUALIFICATION',
        updatedAt: *************,
        TTL: **********,
        // Note: No toQualification method - this is a plain JavaScript object
      } as QualificationDB;

      const dynamodbClient = service.dynamodbClient;
      jest
        .spyOn(dynamodbClient, 'queryAll')
        .mockResolvedValue([mockPlainObject]);

      // This should now work because we use QualificationDB.from() to create proper instances
      const result = await service.getQualificationsForAccount('test-account');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        qualificationId: 'test-qualification',
        description: 'Test qualification description',
        account: 'test-account',
        entityType: 'QUALIFICATION',
      });
    });
  });
});
