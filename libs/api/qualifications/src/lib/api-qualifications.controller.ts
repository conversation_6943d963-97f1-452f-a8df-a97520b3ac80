import { Controller, Get } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';

import {
  AuthorizerContext,
  InjectAuthorizerContext,
} from '@shift-management/api/util/authorizer-context';

import { ApiQualificationsService } from './api-qualifications.service';
import { QualificationResponse } from './qualification.entity';

@ApiTags('qualifications')
@Controller('qualifications')
export class ApiQualificationsController {
  constructor(
    private readonly qualificationsService: ApiQualificationsService,
  ) {}

  @Get('')
  @ApiOperation({
    summary: 'Get qualifications for the current account',
    description:
      'Retrieves all qualifications associated with the current account from the authorizer context',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved qualifications',
    type: QualificationResponse,
  })
  @ApiResponse({
    status: 400,
    description: 'Bad request',
  })
  @ApiResponse({
    status: 500,
    description: 'Internal server error',
  })
  async getQualificationsForAccount(
    @InjectAuthorizerContext() ctx: AuthorizerContext,
  ): Promise<QualificationResponse> {
    const qualifications =
      await this.qualificationsService.getQualificationsForAccount(ctx.account);
    return new QualificationResponse(qualifications);
  }
}
