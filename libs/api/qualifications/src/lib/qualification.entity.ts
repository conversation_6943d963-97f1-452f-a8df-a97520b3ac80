/* eslint-disable max-classes-per-file */
import { ApiProperty } from '@nestjs/swagger';

export interface OAuthTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export class QualificationText {
  @ApiProperty({
    description: 'Language locale code (e.g., en, de, fr)',
    example: 'en',
  })
  locale!: string;

  @ApiProperty({
    description: 'Localized description of the qualification',
    example: 'Quality assurance department',
  })
  description!: string;
}

export interface QualificationFromAPI {
  qualificationID: string;
  description: string;
  texts: QualificationText[];
}

export interface CustomerQualifications {
  Customer: string;
  value: QualificationFromAPI[];
}

export interface QualificationsResponseData {
  Customers: CustomerQualifications[];
}

export interface AccountQualifications {
  account: string;
  value: Qualification[];
}

export interface SecretValue {
  client_id: string;
  client_secret: string;
}

export class Qualification {
  @ApiProperty({
    description: 'Unique identifier for the qualification',
    example: 'Quality',
  })
  qualificationId!: string;

  @ApiProperty({
    description: 'Primary description of the qualification',
    example: 'Quality assurance department',
  })
  description!: string;

  @ApiProperty({
    description: 'Account name this qualification belongs to',
    example: 'ssl-gbr-knaresborough',
  })
  account!: string;

  @ApiProperty({
    description: 'Entity type identifier',
    example: 'QUALIFICATION',
  })
  entityType!: string;

  @ApiProperty({
    description: 'Multilingual text descriptions for the qualification',
    type: [QualificationText],
    required: false,
  })
  texts?: QualificationText[];
}

export class QualificationDB extends Qualification {
  PK!: string; // A#accountName
  SK!: string; // Q#qualificationID
  updatedAt!: number;
  TTL?: number;

  toQualification(): Qualification {
    return {
      qualificationId: this.qualificationId,
      description: this.description,
      account: this.account,
      entityType: this.entityType,
      texts: this.texts,
    };
  }

  static from(item: QualificationDB): QualificationDB {
    const qualification = new QualificationDB();
    // DynamoDB properties
    qualification.PK = item.PK;
    qualification.SK = item.SK;
    qualification.updatedAt = item.updatedAt;
    qualification.TTL = item.TTL;

    // Inherited Qualification properties
    qualification.qualificationId = item.qualificationId;
    qualification.description = item.description;
    qualification.account = item.account;
    qualification.entityType = item.entityType;
    qualification.texts = item.texts;

    return qualification;
  }
}

export class QualificationResponse {
  qualifications: Qualification[];
  constructor(qualifications: Qualification[]) {
    this.qualifications = qualifications;
  }
}
