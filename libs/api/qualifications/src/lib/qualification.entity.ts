/* eslint-disable max-classes-per-file */
export class Qualification {
  qualificationId!: string;
  description!: string;
  account!: string;
  entityType!: string;
}

export class QualificationDB extends Qualification {
  PK!: string; // A#accountName
  SK!: string; // Q#qualificationID
  updatedAt!: number;
  TTL?: number;

  toQualification(): Qualification {
    return {
      qualificationId: this.qualificationId,
      description: this.description,
      account: this.account,
      entityType: this.entityType,
    };
  }

  static from(item: Record<string, unknown>): QualificationDB {
    const qualification = new QualificationDB();
    // DynamoDB properties
    qualification.PK = item['PK'] as string;
    qualification.SK = item['SK'] as string;
    qualification.updatedAt = item['updatedAt'] as number;
    qualification.TTL = item['TTL'] as number;

    // Inherited Qualification properties
    qualification.qualificationId = item['qualificationId'] as string;
    qualification.description = item['description'] as string;
    qualification.account = item['account'] as string;
    qualification.entityType = item['entityType'] as string;

    return qualification;
  }
}

export class QualificationResponse {
  qualifications: Qualification[];
  constructor(qualifications: Qualification[]) {
    this.qualifications = qualifications;
  }
}
