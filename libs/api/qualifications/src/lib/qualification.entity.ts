/* eslint-disable max-classes-per-file */
export interface OAuthTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export class QualificationText {
  locale!: string;
  description!: string;
}

export interface QualificationFromAPI {
  qualificationID: string;
  description: string;
  texts: QualificationText[];
}

export interface CustomerQualifications {
  Customer: string;
  value: QualificationFromAPI[];
}

export interface QualificationsResponseData {
  Customers: CustomerQualifications[];
}

export interface AccountQualifications {
  account: string;
  value: Qualification[];
}

export interface SecretValue {
  client_id: string;
  client_secret: string;
}

export class Qualification {
  qualificationId!: string;
  description!: string;
  account!: string;
  entityType!: string;
  texts?: QualificationText[];
}

export class QualificationDB extends Qualification {
  PK!: string; // A#accountName
  SK!: string; // Q#qualificationID
  updatedAt!: number;

  toQualification(): Qualification {
    return {
      qualificationId: this.qualificationId,
      description: this.description,
      account: this.account,
      entityType: this.entityType,
      texts: this.texts,
    };
  }

  static from(item: QualificationDB): QualificationDB {
    const qualification = new QualificationDB();
    // DynamoDB properties
    qualification.PK = item.PK;
    qualification.SK = item.SK;
    qualification.updatedAt = item.updatedAt;

    // Inherited Qualification properties
    qualification.qualificationId = item.qualificationId;
    qualification.description = item.description;
    qualification.account = item.account;
    qualification.entityType = item.entityType;
    qualification.texts = item.texts;

    return qualification;
  }
}

export class QualificationResponse {
  qualifications: Qualification[];
  constructor(qualifications: Qualification[]) {
    this.qualifications = qualifications;
  }
}
