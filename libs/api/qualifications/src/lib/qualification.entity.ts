export class Qualification {
  qualificationId!: string;
  description!: string;
  account!: string;
  entityType!: string;
}

export class QualificationDB extends Qualification{
  PK!: string; // A#accountName
  SK!: string; // Q#qualificationID
  updatedAt!: number;
  TTL?: number;

  toQualification(): Qualification {
    return {
      qualificationId: this.SK.replace('Q#', ''),
      description: this.Description,
      account: this.PK.replace('A#', ''),
      lastUpdated: this.LastUpdated,
    };
  }
}
