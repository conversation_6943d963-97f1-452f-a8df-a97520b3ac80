export interface Qualification {
  qualificationId: string;
  description: string;
  account: string;
  lastUpdated: string;
}

export class QualificationDB {
  PK!: string; // A#accountName
  SK!: string; // Q#qualificationID
  EntityType!: string; // 'QUALIFICATION'
  Description!: string;
  LastUpdated!: string;
  TTL?: number;

  toQualification(): Qualification {
    return {
      qualificationId: this.SK.replace('Q#', ''),
      description: this.Description,
      account: this.PK.replace('A#', ''),
      lastUpdated: this.LastUpdated,
    };
  }
}
