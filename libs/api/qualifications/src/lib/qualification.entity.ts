/* eslint-disable max-classes-per-file */
export class Qualification {
  qualificationId!: string;
  description!: string;
  account!: string;
  entityType!: string;
}

export class QualificationDB extends Qualification {
  PK!: string; // A#accountName
  SK!: string; // Q#qualificationID
  updatedAt!: number;
  TTL?: number;

  toQualification(): Qualification {
    return {
      qualificationId: this.qualificationId,
      description: this.description,
      account: this.account,
      entityType: this.entityType,
    };
  }
}

export class QualificationResponse {
  qualifications: Qualification[];
  constructor(qualifications: Qualification[]) {
    this.qualifications = qualifications;
  }
}
