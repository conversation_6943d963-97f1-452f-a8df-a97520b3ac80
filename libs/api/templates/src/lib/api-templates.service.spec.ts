import 'aws-sdk-client-mock-jest';
import {
  DynamoDBDocumentClient,
  PutCommand,
  PutCommandInput,
  QueryCommand,
} from '@aws-sdk/lib-dynamodb';
import { BadRequestException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { mockClient } from 'aws-sdk-client-mock';

import {
  DynamoDbClientModule,
  DynamoDBClientProvider,
  NotFoundError,
} from '@shift-management/api/util/dynamodb-client';

import { ApiTemplatesService } from './api-templates.service';
import { ClassEnum } from '@shift-management/shared/types';

import {
  ScheduleItem,
  Template,
  TemplateDB,
  TemplateType,
} from './template.entity';

describe('ApiTemplatesService', () => {
  const ddbMock = mockClient(DynamoDBDocumentClient);
  const accountName = 'readykit-replay-test';
  let service: ApiTemplatesService;
  let client: DynamoDBClient<PERSON>rovider;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [DynamoDbClientModule],
      providers: [ApiTemplatesService],
    }).compile();
    ddbMock.reset();
    client = module.get(DynamoDBClientProvider);
    service = module.get(ApiTemplatesService);
  });

  it('should be defined', () => {
    expect(service).toBeTruthy();
  });

  describe('findAll', () => {
    it('should use the correct query command', async () => {
      // arrange
      ddbMock.on(QueryCommand).resolves({
        Items: [
          {
            PK: 'A#readykit-replay-test',
            SK: 'T#DT#15acfa51-af94-40db-b0e8-ffd0d3875bff',
          },
        ],
      });

      const input_criteria: Partial<Template> = {
        account: 'readykit-replay-test',
      };

      const expected_command = {
        KeyConditionExpression: 'PK = :account and begins_with(SK, :template)',
        ExpressionAttributeValues: {
          ':account': 'A#readykit-replay-test',
          ':template': 'T#',
        },
      };

      //act
      await service.findAll(input_criteria);

      //assert
      expect(ddbMock).toHaveReceivedCommandWith(QueryCommand, expected_command);
    });
  });

  describe('templateNameExists', () => {
    beforeEach(() => {
      ddbMock.on(QueryCommand).resolves({
        Items: [
          {
            PK: 'A#readykit-replay-test',
            SK: 'T#DT#15acfa51-af94-40db-b0e8-ffd0d3875bff',
            name: 'test',
            templateId: '15acfa51-af94-40db-b0e8-ffd0d3875bff',
          },
        ],
      });
    });

    it('throws an error when "name" attribute is missing', async () => {
      //act
      const temp: Partial<Template> = {
        account: 'readykit-replay-test',
      };

      await expect(async () => {
        await service.templateNameExists(temp.name, accountName);
      }).rejects.toThrow(BadRequestException);

      await expect(async () => {
        await service.templateNameExists(temp.name, accountName);
      }).rejects.toThrow('Missing attribute: name');
    });

    it('returns true if there is a template with the same name', async () => {
      // arrange

      const temp: Partial<Template> = {
        account: 'readykit-replay-test',
        name: 'test',
      };

      //act
      const nameExists = await service.templateNameExists(
        temp.name,
        accountName,
      );

      //assert
      expect(nameExists).toBeTruthy();
    });

    it('returns false if there is no template with the same name', async () => {
      // arrange

      const temp: Partial<Template> = {
        account: 'readykit-replay-test',
        name: 'test1',
      };

      //act
      const nameExists = await service.templateNameExists(
        temp.name,
        accountName,
      );

      //assert
      expect(nameExists).toBeFalsy();
    });

    it('should filter excludeId', async () => {
      // arrange

      const temp: Partial<Template> = {
        account: 'readykit-replay-test',
        name: 'test',
      };

      //act
      const nameExists = await service.templateNameExists(
        temp.name,
        accountName,
        '15acfa51-af94-40db-b0e8-ffd0d3875bff',
      );

      //assert
      expect(nameExists).toBeFalsy();
    });
  });

  describe('create', () => {
    const template = new Template();
    beforeEach(() => {
      template.name = 'name';
      template.type = TemplateType.Predefined;
      template.id = 'templateId';
      template.account = 'account';
      template.schedule = [];
      template.shiftsPerDay = -4;
      template.shiftStartTime = 'shiftStartTime';
    });

    it('should send PutCommand', async () => {
      //act
      await service.create(template);

      //assert
      expect(ddbMock).toHaveReceivedCommand(PutCommand);
    });

    it('should contain PK and SK', async () => {
      //arrange
      const expectedCommandInput = {
        Item: expect.objectContaining({
          PK: 'A#account',
          SK: 'T#UT#templateId',
        }),
      };

      //act
      await service.create(template);

      //assert
      expect(ddbMock).toHaveReceivedCommandWith(
        PutCommand,
        expectedCommandInput as PutCommandInput,
      );
    });

    it('should contain createdAt and updatedAt', async () => {
      //arrange
      const expectedCommandInput = {
        Item: expect.objectContaining({
          createdAt: expect.any(Number),
          updatedAt: expect.any(Number),
        }),
      };

      //act
      await service.create(template);

      //assert
      expect(ddbMock).toHaveReceivedCommandWith(
        PutCommand,
        expectedCommandInput as PutCommandInput,
      );
    });

    it('should contain properties of template', async () => {
      //arrange
      const expectedCommandInput = {
        Item: expect.objectContaining({
          name: 'name',
          type: TemplateType.Predefined,
          account: 'account',
          schedule: [],
          shiftsPerDay: -4,
          shiftStartTime: 'shiftStartTime',
        }),
      };

      //act
      await service.create(template);

      //assert
      expect(ddbMock).toHaveReceivedCommandWith(
        PutCommand,
        expectedCommandInput as PutCommandInput,
      );
    });

    it('should contain entityType', async () => {
      //arrange
      const expectedCommandInput = {
        Item: expect.objectContaining({
          entityType: 'template',
        }),
      };

      //act
      await service.create(template);

      //assert
      expect(ddbMock).toHaveReceivedCommandWith(
        PutCommand,
        expectedCommandInput as PutCommandInput,
      );
    });

    it('should contain templateId', async () => {
      //arrange
      const notExpectedCommandInput = {
        Item: expect.not.objectContaining({
          id: 'templateId',
        }),
      };

      const expectedCommandInput = {
        Item: expect.objectContaining({
          templateId: 'templateId',
        }),
      };

      //act
      await service.create(template);

      //assert
      expect(ddbMock).toHaveReceivedCommandWith(
        PutCommand,
        expectedCommandInput as PutCommandInput,
      );

      expect(ddbMock).toHaveReceivedCommandWith(
        PutCommand,
        notExpectedCommandInput as PutCommandInput,
      );
    });

    it('should return template', async () => {
      //act
      const result = await service.create(template);

      //assert
      expect(result).toEqual(template);
    });
  });

  describe('update', () => {
    const id = 'id';
    const account = 'account';
    const name = 'name';
    const schedule: ScheduleItem[] = [];
    const templateDb: TemplateDB = {
      PK: 'A',
      SK: 'B',
      createdAt: 1,
      updatedAt: 2,
      entityType: 'template',
      name: 'name',
      type: TemplateType.UserDefined,
      templateId: 'id',
      account: 'account',
      schedule: [],
      shiftsPerDay: 3,
      shiftStartTime: '0:00',
    };
    let spy: jest.SpyInstance;
    beforeEach(() => {
      spy = jest.spyOn(client, 'updateItem');
    });

    it('should invoke updateItem', async () => {
      // arrange
      spy.mockReturnValue(Promise.resolve(templateDb));
      //act
      await service.update(id, account, schedule, name);

      //assert
      expect(spy).toHaveBeenCalled();
    });

    it('should return Template', async () => {
      // arrange
      spy.mockReturnValue(Promise.resolve(templateDb));
      const expectedTemplate = Template.from(templateDb);

      //act
      const result = await service.update(id, account, schedule, name);

      //assert
      expect(result).toEqual(expectedTemplate);
    });

    it('should set primary key', async () => {
      // arrange
      spy.mockReturnValue(Promise.resolve(templateDb));

      //act
      await service.update(id, account, schedule, name);

      //assert
      expect(spy).toHaveBeenCalledWith(
        expect.objectContaining({
          Key: {
            PK: 'A#account',
            SK: 'T#UT#id',
          },
        }),
      );
    });

    it('should throw BadRequestException when neither name nor schedule is given', async () => {
      //assert
      await expect(
        service.update(id, account, undefined, undefined),
      ).rejects.toThrow(BadRequestException);
    });

    it('should throw BadRequestException when service throws NotFoundError', async () => {
      spy.mockImplementation(() => {
        throw new NotFoundError();
      });

      //assert
      await expect(service.update(id, account, schedule, name)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should update template with schedule items containing qualifications', async () => {
      // arrange
      const scheduleWithQualifications: ScheduleItem[] = [
        {
          class: ClassEnum.Production,
          start: 480, // 8:00 AM in minutes
          end: 960, // 4:00 PM in minutes
          name: 'Morning Production Shift',
          qualification: 'ShrinkTBC',
        },
        {
          class: ClassEnum.Production,
          start: 960, // 4:00 PM in minutes
          end: 1440, // 12:00 AM in minutes
          name: 'Evening Production Shift',
          qualification: 'Laboratory',
        },
        {
          class: ClassEnum.Production,
          start: 0, // 12:00 AM in minutes
          end: 480, // 8:00 AM in minutes
          name: 'Night Production Shift',
          qualification: 'Quality',
        },
      ];

      const templateDbWithQualifications: TemplateDB = {
        ...templateDb,
        schedule: scheduleWithQualifications,
      };

      spy.mockReturnValue(Promise.resolve(templateDbWithQualifications));

      // act
      const result = await service.update(id, account, scheduleWithQualifications, name);

      // assert
      expect(spy).toHaveBeenCalled();
      expect(result.schedule).toEqual(scheduleWithQualifications);
      expect(result.schedule[0].qualification).toBe('ShrinkTBC');
      expect(result.schedule[1].qualification).toBe('Laboratory');
      expect(result.schedule[2].qualification).toBe('Quality');
    });

    it('should update template with mixed schedule items (some with qualifications, some without)', async () => {
      // arrange
      const mixedSchedule: ScheduleItem[] = [
        {
          class: ClassEnum.Production,
          start: 480, // 8:00 AM in minutes
          end: 960, // 4:00 PM in minutes
          name: 'Morning Production Shift',
          qualification: 'ShrinkTBC',
        },
        {
          class: ClassEnum.Production,
          start: 960, // 4:00 PM in minutes
          end: 1440, // 12:00 AM in minutes
          name: 'Evening Production Shift',
          // No qualification property
        },
      ];

      const templateDbWithMixedSchedule: TemplateDB = {
        ...templateDb,
        schedule: mixedSchedule,
      };

      spy.mockReturnValue(Promise.resolve(templateDbWithMixedSchedule));

      // act
      const result = await service.update(id, account, mixedSchedule, name);

      // assert
      expect(spy).toHaveBeenCalled();
      expect(result.schedule).toEqual(mixedSchedule);
      expect(result.schedule[0].qualification).toBe('ShrinkTBC');
      expect(result.schedule[1].qualification).toBeUndefined();
    });
  });

  describe('getTemplatesForAllAccounts', () => {
    it('should return expected Map', async () => {
      // arrange
      ddbMock.on(QueryCommand).resolves({
        Items: [
          {
            PK: 'A#readykit-replay-test',
            SK: 'T#DT#15acfa51-af94-40db-b0e8-ffd0d3875bff',
          },
          {
            PK: 'A#readykit-replay-test',
            SK: 'T#DT#15acfa51-af94-40db-b0e8-ffd0d3875bfy',
          },
        ],
      });

      const expectResult = new Map();
      expectResult.set('readykit-replay-test', [
        {
          PK: 'A#readykit-replay-test',
          SK: 'T#DT#15acfa51-af94-40db-b0e8-ffd0d3875bff',
        },
        {
          PK: 'A#readykit-replay-test',
          SK: 'T#DT#15acfa51-af94-40db-b0e8-ffd0d3875bfy',
        },
      ]);

      //act
      const result = await service.getTemplatesForAllAccounts(
        new Set(['readykit-replay-test']),
      );

      //assert
      expect(result.size).toBe(1);
      expect(result).toEqual(expectResult);
    });
  });

  describe('isTemplateScheduleOverlapping', () => {
    const template = new Template();
    beforeEach(() => {
      template.name = 'name';
      template.type = TemplateType.Predefined;
      template.id = 'templateId';
      template.account = 'account';
      template.shiftsPerDay = -4;
      template.shiftStartTime = 'shiftStartTime';
    });

    it('throws an error when "schedule" attribute is missing', () => {
      //act
      expect(() => {
        service.isTemplateScheduleOverlapping(template);
      }).toThrow(BadRequestException);

      expect(() => {
        service.isTemplateScheduleOverlapping(template);
      }).toThrow('Missing attribute: schedule');
    });
  });

  describe('isPredefinedTemplate', () => {
    describe('when the template is predefined', () => {
      const template = new Template();
      beforeEach(() => {
        template.name = 'name';
        template.type = TemplateType.Predefined;
        template.id = 'templateId';
        template.account = 'account';
        template.shiftsPerDay = -4;
        template.shiftStartTime = 'shiftStartTime';
      });

      it('should return true', async () => {
        ddbMock.on(QueryCommand).resolves({
          Items: [template],
        });
        const result = await service.isPredefinedTemplate('account', 'a');

        expect(result).toBe(true);
      });
    });

    describe('when the template is user defined', () => {
      const template = new Template();
      beforeEach(() => {
        template.name = 'name';
        template.type = TemplateType.UserDefined;
        template.id = 'templateId';
        template.account = 'account';
        template.shiftsPerDay = -4;
        template.shiftStartTime = 'shiftStartTime';
      });

      it('should return false', async () => {
        ddbMock.on(QueryCommand).resolves({
          Items: [template],
        });
        const result = await service.isPredefinedTemplate(
          'account',
          'templateId',
        );

        expect(result).toBe(false);
      });
    });
  });
});
