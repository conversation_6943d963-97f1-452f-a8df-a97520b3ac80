/* eslint-disable max-classes-per-file */

import {
  IsEnum,
  IsInt,
  IsNotEmpty,
  IsOptional,
  IsString,
} from 'class-validator';

import { ClassEnum, ScheduleType } from '@shift-management/shared/types';

import {
  IsMultipleOfQuarterHour,
  IsValidEnd,
} from './api-instances.validation';

export class Instance {
  name!: string;
  entityType!: string;
  class: ClassEnum = ClassEnum.Production;
  start!: number;
  end!: number;
  account!: string;
  lineId!: string;
  instanceId!: string;
  templateId?: string;
  qualification?: string;

  static from(instance: InstanceDB): Instance {
    const newInstance = new Instance();
    newInstance.name = instance.name;
    newInstance.entityType = instance.entityType;
    newInstance.class = instance.class;
    newInstance.start = instance.start;
    newInstance.end = instance.end;
    newInstance.account = instance.account;
    newInstance.lineId = instance.lineId;
    newInstance.instanceId = instance.instanceId;
    newInstance.templateId = instance.templateId;
    newInstance.qualification = instance.qualification;
    return newInstance;
  }
}

export class CreateInstanceDTO {
  @IsMultipleOfQuarterHour()
  @IsInt()
  start!: number;
  @IsMultipleOfQuarterHour()
  @IsValidEnd('start')
  @IsInt()
  end!: number;
  @IsString()
  @IsNotEmpty()
  name!: string;
  @IsEnum(ClassEnum)
  class!: ClassEnum;
  @IsOptional()
  @IsString()
  qualification?: string;
}

export class UpdateInstanceDTO {
  @IsMultipleOfQuarterHour()
  @IsOptional()
  @IsInt()
  start?: number;
  @IsMultipleOfQuarterHour()
  @IsValidEnd('start')
  @IsInt()
  @IsOptional()
  end?: number;
  @IsString()
  @IsOptional()
  @IsNotEmpty()
  name?: string;
  @IsEnum(ClassEnum)
  @IsOptional()
  class?: ClassEnum;
  @IsOptional()
  @IsString()
  qualification?: string;
}

export class InstanceDB extends Instance {
  PK!: string;
  SK!: string;
  createdAt!: number;
  updatedAt!: number;
  // eslint-disable-next-line @typescript-eslint/naming-convention
  LSI1_SK!: number;
}

export class ScheduleInstance extends InstanceDB {
  scheduleType!: ScheduleType;
}

export class InstanceResponse {
  instances: Instance[];
  constructor(instances: InstanceDB[]) {
    this.instances = instances.map((instance) => Instance.from(instance));
  }
}
