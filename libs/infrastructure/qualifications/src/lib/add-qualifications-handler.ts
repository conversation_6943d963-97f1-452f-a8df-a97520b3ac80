import { resolve } from 'path';

import { aws_lambda as lambda } from 'aws-cdk-lib';
import { ITable } from 'aws-cdk-lib/aws-dynamodb';
import { Construct } from 'constructs';

import { LambdaConstruct } from '@shift-management/infrastructure/constructs';

/**
 * Adds qualifications Lambda handler to an existing API Gateway
 * This creates the Lambda function and returns it so it can be integrated into existing API Gateway routes
 * @param stack - The CDK construct scope
 * @param table - The DynamoDB table to grant read access to
 * @param stage - The deployment stage
 * @returns The created Lambda function
 */
export const addQualificationsHandler = (
  stack: Construct,
  table: ITable,
  stage: string,
): lambda.Function => {
  const apiHandler = 'main.handler';
  const env = {
    shiftDataTableName: table.tableName,
    logLevel: 'WARN',
  };

  const apiQualificationsDistPath = resolve(
    './dist/apps/api/api-qualifications',
  );
  const apiQualificationsLambda = new LambdaConstruct(
    stack,
    'shifts-api-qualifications-lambda',
    apiHandler,
    apiQualificationsDistPath,
    env,
    apiQualificationsDistPath,
    stage,
  );
  // Grant read access to the DynamoDB table for qualifications
  table.grantReadData(apiQualificationsLambda.lambdaFunction);

  return apiQualificationsLambda.lambdaFunction;
};
